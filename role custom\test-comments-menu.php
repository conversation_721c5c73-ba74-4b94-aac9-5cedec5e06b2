<?php
/**
 * Test dosyası - Yorumlar menüsü ekleme testi
 * Bu dosyayı WordPress admin panelinde çalıştırarak test edebilirsiniz
 *
 * Kullanım: WordPress admin panelinde bu dosyayı çalıştırın
 * Örnek: wp-admin/admin.php?page=test-comments-menu
 */

// WordPress yüklenmişse devam et
if (!defined('ABSPATH')) {
    die('WordPress yüklenmemiş!');
}

echo '<div style="padding: 20px; background: #f1f1f1; margin: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">';
echo '<h2 style="color: #333; margin-top: 0;">🧪 Role Custom - Yorumlar Menüsü Test Sonuçları</h2>';
echo '<p style="color: #666; margin-bottom: 20px;">Test zamanı: ' . current_time('Y-m-d H:i:s') . '</p>';

// 1. Role Custom eklentisi aktif mi?
if (class_exists('Role_Custom')) {
    echo '<p style="color: green;">✅ Role Custom eklentisi aktif</p>';
    
    $role_custom = Role_Custom::get_instance();
    
    // 2. show_comments_menu fonksiyonu var mı?
    if (method_exists($role_custom, 'show_comments_menu')) {
        echo '<p style="color: green;">✅ show_comments_menu fonksiyonu mevcut</p>';
    } else {
        echo '<p style="color: red;">❌ show_comments_menu fonksiyonu bulunamadı</p>';
    }
    
} else {
    echo '<p style="color: red;">❌ Role Custom eklentisi aktif değil</p>';
}

// 3. Mevcut kullanıcı tutor instructor mı?
$current_user = wp_get_current_user();
if (in_array('tutor_instructor', $current_user->roles)) {
    echo '<p style="color: green;">✅ Mevcut kullanıcı tutor_instructor rolünde</p>';
} else {
    echo '<p style="color: orange;">⚠️ Mevcut kullanıcı tutor_instructor rolünde değil (Roles: ' . implode(', ', $current_user->roles) . ')</p>';
}

// 4. Kullanıcının moderate_comments yetkisi var mı?
if (current_user_can('moderate_comments')) {
    echo '<p style="color: green;">✅ Kullanıcının moderate_comments yetkisi var</p>';
} else {
    echo '<p style="color: red;">❌ Kullanıcının moderate_comments yetkisi yok</p>';
}

// 5. WordPress admin menülerini kontrol et
global $menu, $submenu;
$comments_menu_found = false;
$comments_submenu_found = false;

if (is_array($menu)) {
    echo '<h4 style="color: #333; margin-top: 20px;">📋 Mevcut Admin Menüleri:</h4>';
    echo '<div style="background: #fff; padding: 15px; border-radius: 4px; margin: 10px 0; max-height: 300px; overflow-y: auto;">';
    echo '<table style="width: 100%; border-collapse: collapse; font-size: 12px;">';
    echo '<tr style="background: #f9f9f9;"><th style="border: 1px solid #ddd; padding: 5px;">Pozisyon</th><th style="border: 1px solid #ddd; padding: 5px;">Menü Adı</th><th style="border: 1px solid #ddd; padding: 5px;">Slug</th></tr>';

    $comments_menu_count = 0;
    foreach ($menu as $position => $menu_item) {
        if (isset($menu_item[2])) {
            $menu_name = $menu_item[0] ?? 'Bilinmiyor';
            $menu_slug = $menu_item[2];

            $row_color = '';
            if ($menu_slug === 'edit-comments.php') {
                $comments_menu_found = true;
                $comments_menu_count++;
                $row_color = 'background: #e8f5e8;';
            } elseif (strpos($menu_slug, 'shop_coupon') !== false) {
                $row_color = 'background: #fff3cd;';
            } elseif (strpos($menu_slug, 'wc-orders') !== false || strpos($menu_slug, 'customer') !== false) {
                $row_color = 'background: #d1ecf1;';
            }

            echo '<tr style="' . $row_color . '">';
            echo '<td style="border: 1px solid #ddd; padding: 5px;">' . $position . '</td>';
            echo '<td style="border: 1px solid #ddd; padding: 5px;">' . strip_tags($menu_name) . '</td>';
            echo '<td style="border: 1px solid #ddd; padding: 5px;">' . $menu_slug . '</td>';
            echo '</tr>';
        }
    }
    echo '</table>';
    echo '</div>';

    // Çift menü kontrolü
    if ($comments_menu_count > 1) {
        echo '<p style="color: red;">❌ Ana menüde yorumlar menüsü ' . $comments_menu_count . ' kez bulundu (çift menü sorunu)</p>';
    } elseif ($comments_menu_count === 1) {
        echo '<p style="color: orange;">⚠️ Ana menüde yorumlar menüsü bulundu (artık alt menü olmalı)</p>';
    } else {
        echo '<p style="color: green;">✅ Ana menüde yorumlar menüsü yok (doğru durum - artık alt menü)</p>';
    }
}

// Tutor LMS alt menüsünü kontrol et
if (isset($submenu['tutor']) && is_array($submenu['tutor'])) {
    echo '<h4 style="color: #333; margin-top: 20px;">📋 Tutor LMS Alt Menüleri:</h4>';
    echo '<div style="background: #fff; padding: 15px; border-radius: 4px; margin: 10px 0;">';
    echo '<table style="width: 100%; border-collapse: collapse; font-size: 12px;">';
    echo '<tr style="background: #f9f9f9;"><th style="border: 1px solid #ddd; padding: 5px;">Sıra</th><th style="border: 1px solid #ddd; padding: 5px;">Menü Adı</th><th style="border: 1px solid #ddd; padding: 5px;">Slug</th></tr>';

    foreach ($submenu['tutor'] as $index => $submenu_item) {
        $submenu_name = $submenu_item[0] ?? 'Bilinmiyor';
        $submenu_slug = $submenu_item[2] ?? '';

        $row_color = '';
        if ($submenu_slug === 'edit-comments.php') {
            $comments_submenu_found = true;
            $row_color = 'background: #e8f5e8;';
        }

        echo '<tr style="' . $row_color . '">';
        echo '<td style="border: 1px solid #ddd; padding: 5px;">' . $index . '</td>';
        echo '<td style="border: 1px solid #ddd; padding: 5px;">' . strip_tags($submenu_name) . '</td>';
        echo '<td style="border: 1px solid #ddd; padding: 5px;">' . $submenu_slug . '</td>';
        echo '</tr>';
    }
    echo '</table>';
    echo '</div>';

    if ($comments_submenu_found) {
        echo '<p style="color: green;">✅ Yorumlar menüsü Tutor LMS alt menüsünde bulundu</p>';
    } else {
        echo '<p style="color: red;">❌ Yorumlar menüsü Tutor LMS alt menüsünde bulunamadı</p>';
    }
} else {
    echo '<p style="color: red;">❌ Tutor LMS menüsü bulunamadı</p>';
}
}

if (!$comments_menu_found) {
    echo '<p style="color: red;">❌ Yorumlar menüsü WordPress admin menüsünde bulunamadı</p>';
}

// Alt menü kontrolü
global $submenu;
echo '<h4 style="color: #333; margin-top: 20px;">📋 Yorumlar Menüsü Alt Sekmeler:</h4>';
if (isset($submenu['edit-comments.php']) && !empty($submenu['edit-comments.php'])) {
    echo '<p style="color: orange;">⚠️ Yorumlar menüsünde alt sekmeler var (' . count($submenu['edit-comments.php']) . ' adet)</p>';
    echo '<div style="background: #fff3cd; padding: 10px; border-radius: 4px; margin: 10px 0;">';
    foreach ($submenu['edit-comments.php'] as $submenu_item) {
        echo '<p style="margin: 5px 0;">• ' . $submenu_item[0] . ' (' . $submenu_item[2] . ')</p>';
    }
    echo '</div>';
} else {
    echo '<p style="color: green;">✅ Yorumlar menüsünde alt sekme yok (istenen durum)</p>';
}

// 6. Tutor LMS aktif mi?
if (class_exists('TUTOR\Tutor')) {
    echo '<p style="color: green;">✅ Tutor LMS aktif</p>';
} else {
    echo '<p style="color: orange;">⚠️ Tutor LMS aktif değil</p>';
}

// 7. Hook'ların eklenip eklenmediğini kontrol et
$admin_head_hooks = $GLOBALS['wp_filter']['admin_head'] ?? null;
$admin_footer_hooks = $GLOBALS['wp_filter']['admin_footer'] ?? null;
$admin_print_scripts_hooks = $GLOBALS['wp_filter']['admin_print_scripts'] ?? null;
$css_hook_found = false;
$js_hook_found = false;
$force_hook_found = false;

if ($admin_head_hooks) {
    foreach ($admin_head_hooks->callbacks as $priority => $callbacks) {
        foreach ($callbacks as $callback) {
            if (isset($callback['function']) && is_array($callback['function'])) {
                $object = $callback['function'][0] ?? null;
                $method = $callback['function'][1] ?? null;

                if ($object instanceof Role_Custom && $method === 'show_comments_menu_css') {
                    $css_hook_found = true;
                    echo '<p style="color: green;">✅ show_comments_menu_css hook\'u admin_head\'e eklenmiş (Priority: ' . $priority . ')</p>';
                    break 2;
                }
            }
        }
    }
}

if ($admin_footer_hooks) {
    foreach ($admin_footer_hooks->callbacks as $priority => $callbacks) {
        foreach ($callbacks as $callback) {
            if (isset($callback['function']) && is_array($callback['function'])) {
                $object = $callback['function'][0] ?? null;
                $method = $callback['function'][1] ?? null;

                if ($object instanceof Role_Custom && $method === 'show_comments_menu_js') {
                    $js_hook_found = true;
                    echo '<p style="color: green;">✅ show_comments_menu_js hook\'u admin_footer\'a eklenmiş (Priority: ' . $priority . ')</p>';
                    break 2;
                }
            }
        }
    }
}

if (!$css_hook_found) {
    echo '<p style="color: red;">❌ show_comments_menu_css hook\'u admin_head\'e eklenmemiş</p>';
}

if ($admin_print_scripts_hooks) {
    foreach ($admin_print_scripts_hooks->callbacks as $priority => $callbacks) {
        foreach ($callbacks as $callback) {
            if (isset($callback['function']) && is_array($callback['function'])) {
                $object = $callback['function'][0] ?? null;
                $method = $callback['function'][1] ?? null;

                if ($object instanceof Role_Custom && $method === 'force_add_comments_menu') {
                    $force_hook_found = true;
                    echo '<p style="color: green;">✅ force_add_comments_menu hook\'u admin_print_scripts\'e eklenmiş (Priority: ' . $priority . ')</p>';
                    break 2;
                }
            }
        }
    }
}

if (!$css_hook_found) {
    echo '<p style="color: red;">❌ show_comments_menu_css hook\'u admin_head\'e eklenmemiş</p>';
}

if (!$js_hook_found) {
    echo '<p style="color: red;">❌ show_comments_menu_js hook\'u admin_footer\'a eklenmemiş</p>';
}

if (!$force_hook_found) {
    echo '<p style="color: red;">❌ force_add_comments_menu hook\'u admin_print_scripts\'e eklenmemiş</p>';
}

// 8. Tutor LMS'in filter_posts_for_instructors fonksiyonunu kontrol et
if (class_exists('TUTOR\Admin')) {
    echo '<p style="color: green;">✅ Tutor Admin sınıfı mevcut</p>';

    // Tutor Admin instance'ını al
    $tutor_admin = new \TUTOR\Admin();
    if (method_exists($tutor_admin, 'filter_posts_for_instructors')) {
        echo '<p style="color: green;">✅ filter_posts_for_instructors fonksiyonu mevcut</p>';

        // admin_init hook'unda bu fonksiyonun var olup olmadığını kontrol et
        $admin_init_hooks = $GLOBALS['wp_filter']['admin_init'] ?? null;
        $tutor_hook_found = false;

        if ($admin_init_hooks) {
            foreach ($admin_init_hooks->callbacks as $priority => $callbacks) {
                foreach ($callbacks as $callback) {
                    if (isset($callback['function']) && is_array($callback['function'])) {
                        $object = $callback['function'][0] ?? null;
                        $method = $callback['function'][1] ?? null;

                        if ($object instanceof \TUTOR\Admin && $method === 'filter_posts_for_instructors') {
                            $tutor_hook_found = true;
                            echo '<p style="color: orange;">⚠️ Tutor LMS filter_posts_for_instructors hook\'u hala aktif (Priority: ' . $priority . ')</p>';
                            break 2;
                        }
                    }
                }
            }
        }

        if (!$tutor_hook_found) {
            echo '<p style="color: green;">✅ Tutor LMS filter_posts_for_instructors hook\'u başarıyla kaldırıldı</p>';
        }

    } else {
        echo '<p style="color: red;">❌ filter_posts_for_instructors fonksiyonu bulunamadı</p>';
    }
} else {
    echo '<p style="color: orange;">⚠️ Tutor Admin sınıfı bulunamadı</p>';
}

// 9. WordPress admin menü hook'larını listele
echo '<h3 style="color: #333; margin-top: 20px;">🔧 Admin Menu Hook Detayları</h3>';
$admin_menu_hooks = $GLOBALS['wp_filter']['admin_menu'] ?? null;

if ($admin_menu_hooks) {
    echo '<div style="background: #fff; padding: 15px; border-radius: 4px; margin: 10px 0;">';
    echo '<h4>Admin Menu Hook\'ları:</h4>';
    echo '<table style="width: 100%; border-collapse: collapse;">';
    echo '<tr style="background: #f9f9f9;"><th style="border: 1px solid #ddd; padding: 8px;">Priority</th><th style="border: 1px solid #ddd; padding: 8px;">Fonksiyon</th><th style="border: 1px solid #ddd; padding: 8px;">Sınıf</th></tr>';

    foreach ($admin_menu_hooks->callbacks as $priority => $callbacks) {
        foreach ($callbacks as $callback) {
            $function_name = 'Bilinmeyen';
            $class_name = 'N/A';

            if (isset($callback['function'])) {
                if (is_array($callback['function'])) {
                    $object = $callback['function'][0] ?? null;
                    $method = $callback['function'][1] ?? null;

                    if (is_object($object)) {
                        $class_name = get_class($object);
                        $function_name = $method;
                    } elseif (is_string($object)) {
                        $class_name = $object;
                        $function_name = $method;
                    }
                } elseif (is_string($callback['function'])) {
                    $function_name = $callback['function'];
                }
            }

            $row_color = '';
            if (strpos($class_name, 'Role_Custom') !== false) {
                $row_color = 'background: #e8f5e8;';
            } elseif (strpos($class_name, 'TUTOR') !== false) {
                $row_color = 'background: #fff3cd;';
            }

            echo '<tr style="' . $row_color . '">';
            echo '<td style="border: 1px solid #ddd; padding: 8px;">' . $priority . '</td>';
            echo '<td style="border: 1px solid #ddd; padding: 8px;">' . $function_name . '</td>';
            echo '<td style="border: 1px solid #ddd; padding: 8px;">' . $class_name . '</td>';
            echo '</tr>';
        }
    }
    echo '</table>';
    echo '</div>';
}

echo '<hr style="margin: 20px 0;">';
echo '<h3 style="color: #333;">📝 Öneriler ve Çözümler:</h3>';

if (!$comments_menu_found) {
    echo '<div style="background: #fff3cd; padding: 15px; border-radius: 4px; border-left: 4px solid #ffc107;">';
    echo '<h4 style="margin-top: 0; color: #856404;">⚠️ Yorumlar menüsü bulunamadı</h4>';
    echo '<ul style="margin-bottom: 0;">';
    echo '<li>WordPress admin panelini yenileyin (F5 veya Ctrl+R)</li>';
    echo '<li>Role Custom eklentisini devre dışı bırakıp tekrar etkinleştirin</li>';
    echo '<li>Tutor instructor rolünde bir kullanıcı ile giriş yapın</li>';
    echo '<li>Tarayıcı cache\'ini temizleyin</li>';
    echo '<li>Hook priority\'sinin doğru olduğundan emin olun (999999)</li>';
    echo '</ul>';
    echo '</div>';
} else {
    echo '<div style="background: #d4edda; padding: 15px; border-radius: 4px; border-left: 4px solid #28a745;">';
    echo '<h4 style="margin-top: 0; color: #155724;">✅ Başarılı!</h4>';
    echo '<p style="margin-bottom: 0;">Yorumlar menüsü başarıyla eklendi ve görünür durumda.</p>';
    echo '</div>';
}

// Debug bilgileri
echo '<div style="background: #f8f9fa; padding: 15px; border-radius: 4px; margin-top: 20px;">';
echo '<h4 style="margin-top: 0; color: #495057;">🔍 Debug Bilgileri</h4>';
echo '<p><strong>WordPress Sürümü:</strong> ' . get_bloginfo('version') . '</p>';
echo '<p><strong>PHP Sürümü:</strong> ' . PHP_VERSION . '</p>';
echo '<p><strong>Aktif Tema:</strong> ' . wp_get_theme()->get('Name') . '</p>';
echo '<p><strong>Mevcut Sayfa:</strong> ' . (isset($_GET['page']) ? $_GET['page'] : 'Bilinmiyor') . '</p>';
echo '<p><strong>Admin URL:</strong> ' . admin_url() . '</p>';
echo '</div>';

// Gizlenen elementler hakkında bilgi
echo '<h4 style="color: #333; margin-top: 20px;">🚫 Gizlenen Sidebar Elementleri:</h4>';
echo '<div style="background: #fff; padding: 15px; border-radius: 4px; margin: 10px 0;">';
echo '<p>Aşağıdaki CSS seçicileri ile gereksiz boşluk oluşturan elementler gizleniyor:</p>';
echo '<ul style="font-family: monospace; background: #f8f9fa; padding: 10px; border-radius: 3px;">';
echo '<li><code>#adminmenu > li:nth-child(3)</code></li>';
echo '<li><code>#adminmenu > li:nth-child(8)</code></li>';
echo '<li><code>#adminmenu > li:nth-child(9)</code> <span style="color: green;">(YENİ)</span></li>';
echo '<li><code>#adminmenu > li:nth-child(10)</code></li>';
echo '<li><code>#adminmenu > li:nth-child(12)</code></li>';
echo '<li><code>#adminmenu > li:nth-child(13)</code> <span style="color: green;">(YENİ)</span></li>';
echo '<li><code>#menu-media</code></li>';
echo '</ul>';
echo '</div>';

// JavaScript test kodu ekle
echo '<script type="text/javascript">
jQuery(document).ready(function($) {
    console.log("Role Custom Test: Sayfa yüklendi, yorumlar menüsü kontrol ediliyor...");

    setTimeout(function() {
        var commentsMenus = $("#menu-comments");
        var tutorSubmenu = $("#toplevel_page_tutor .wp-submenu");
        var testResults = $("#role-custom-js-test-results");

        if (testResults.length === 0) {
            $("body").append("<div id=\"role-custom-js-test-results\" style=\"position: fixed; top: 50px; right: 20px; background: #fff; border: 2px solid #0073aa; padding: 15px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.2); z-index: 9999; max-width: 350px;\"></div>");
            testResults = $("#role-custom-js-test-results");
        }

        var resultHtml = "<h4 style=\"margin-top: 0; color: #0073aa;\">🔍 JavaScript Test Sonuçları</h4>";

        // Ana menüde yorumlar kontrolü
        if (commentsMenus.length > 1) {
            resultHtml += "<p style=\"color: red;\">❌ Ana menüde " + commentsMenus.length + " adet yorumlar menüsü (çift menü)</p>";
        } else if (commentsMenus.length === 1) {
            resultHtml += "<p style=\"color: orange;\">⚠️ Ana menüde yorumlar menüsü var (artık alt menü olmalı)</p>";
        } else {
            resultHtml += "<p style=\"color: green;\">✅ Ana menüde yorumlar menüsü yok (doğru)</p>";
        }

        // Tutor LMS alt menüsünde yorumlar kontrolü
        var tutorCommentsLink = tutorSubmenu.find("a[href*='edit-comments.php']");
        if (tutorCommentsLink.length > 0) {
            resultHtml += "<p style=\"color: green;\">✅ Tutor LMS alt menüsünde yorumlar bulundu</p>";
            resultHtml += "<p><strong>Link metni:</strong> " + tutorCommentsLink.text().trim() + "</p>";
        } else {
            resultHtml += "<p style=\"color: red;\">❌ Tutor LMS alt menüsünde yorumlar bulunamadı</p>";
        }

        // Tutor LMS menüsü kontrolü
        if (tutorSubmenu.length > 0) {
            resultHtml += "<p style=\"color: green;\">✅ Tutor LMS menüsü bulundu</p>";
            resultHtml += "<p><strong>Alt menü sayısı:</strong> " + tutorSubmenu.find("li").length + "</p>";
        } else {
            resultHtml += "<p style=\"color: red;\">❌ Tutor LMS menüsü bulunamadı</p>";
        }

        // Gizlenen elementleri kontrol et
        var hiddenElements = [
            "#adminmenu > li:nth-child(3)",
            "#adminmenu > li:nth-child(8)",
            "#adminmenu > li:nth-child(9)",
            "#adminmenu > li:nth-child(10)",
            "#adminmenu > li:nth-child(12)",
            "#adminmenu > li:nth-child(13)",
            "#menu-media"
        ];

        var hiddenCount = 0;
        hiddenElements.forEach(function(selector) {
            var element = $(selector);
            if (element.length > 0 && !element.is(":visible")) {
                hiddenCount++;
            }
        });

        resultHtml += "<p><strong>Gizlenen elementler:</strong> " + hiddenCount + "/" + hiddenElements.length + "</p>";

        resultHtml += "<button onclick=\"$(this).parent().remove()\" style=\"background: #0073aa; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer; margin-top: 10px;\">Kapat</button>";

        testResults.html(resultHtml);

        console.log("Role Custom Test: JavaScript test tamamlandı");
    }, 2000); // 2 saniye bekle
});
</script>';

echo '</div>';
