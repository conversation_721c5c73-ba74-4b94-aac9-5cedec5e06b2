<?php
/**
 * Test dosyası - Yorumlar menüsü ekleme testi
 * Bu dosyayı WordPress admin panelinde çalıştırarak test edebilirsiniz
 *
 * Kullanım: WordPress admin panelinde bu dosyayı çalıştırın
 * Örnek: wp-admin/admin.php?page=test-comments-menu
 */

// WordPress yüklenmişse devam et
if (!defined('ABSPATH')) {
    die('WordPress yüklenmemiş!');
}

echo '<div style="padding: 20px; background: #f1f1f1; margin: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">';
echo '<h2 style="color: #333; margin-top: 0;">🧪 Role Custom - Yorumlar Menüsü Test Sonuçları</h2>';
echo '<p style="color: #666; margin-bottom: 20px;">Test zamanı: ' . current_time('Y-m-d H:i:s') . '</p>';

// 1. Role Custom eklentisi aktif mi?
if (class_exists('Role_Custom')) {
    echo '<p style="color: green;">✅ Role Custom eklentisi aktif</p>';
    
    $role_custom = Role_Custom::get_instance();
    
    // 2. show_comments_menu fonksiyonu var mı?
    if (method_exists($role_custom, 'show_comments_menu')) {
        echo '<p style="color: green;">✅ show_comments_menu fonksiyonu mevcut</p>';
    } else {
        echo '<p style="color: red;">❌ show_comments_menu fonksiyonu bulunamadı</p>';
    }
    
} else {
    echo '<p style="color: red;">❌ Role Custom eklentisi aktif değil</p>';
}

// 3. Mevcut kullanıcı tutor instructor mı?
$current_user = wp_get_current_user();
if (in_array('tutor_instructor', $current_user->roles)) {
    echo '<p style="color: green;">✅ Mevcut kullanıcı tutor_instructor rolünde</p>';
} else {
    echo '<p style="color: orange;">⚠️ Mevcut kullanıcı tutor_instructor rolünde değil (Roles: ' . implode(', ', $current_user->roles) . ')</p>';
}

// 4. Kullanıcının moderate_comments yetkisi var mı?
if (current_user_can('moderate_comments')) {
    echo '<p style="color: green;">✅ Kullanıcının moderate_comments yetkisi var</p>';
} else {
    echo '<p style="color: red;">❌ Kullanıcının moderate_comments yetkisi yok</p>';
}

// 5. WordPress admin menülerini kontrol et
global $menu;
$comments_menu_found = false;

if (is_array($menu)) {
    foreach ($menu as $menu_item) {
        if (isset($menu_item[2]) && $menu_item[2] === 'edit-comments.php') {
            $comments_menu_found = true;
            echo '<p style="color: green;">✅ Yorumlar menüsü WordPress admin menüsünde bulundu</p>';
            echo '<p style="color: blue;">📋 Menü detayları: ' . print_r($menu_item, true) . '</p>';
            break;
        }
    }
}

if (!$comments_menu_found) {
    echo '<p style="color: red;">❌ Yorumlar menüsü WordPress admin menüsünde bulunamadı</p>';
}

// 6. Tutor LMS aktif mi?
if (class_exists('TUTOR\Tutor')) {
    echo '<p style="color: green;">✅ Tutor LMS aktif</p>';
} else {
    echo '<p style="color: orange;">⚠️ Tutor LMS aktif değil</p>';
}

// 7. Hook'ların eklenip eklenmediğini kontrol et
$admin_menu_hooks = $GLOBALS['wp_filter']['admin_menu'] ?? null;
$comments_hook_found = false;

if ($admin_menu_hooks) {
    foreach ($admin_menu_hooks->callbacks as $priority => $callbacks) {
        foreach ($callbacks as $callback) {
            if (isset($callback['function']) && is_array($callback['function'])) {
                $object = $callback['function'][0] ?? null;
                $method = $callback['function'][1] ?? null;
                
                if ($object instanceof Role_Custom && $method === 'show_comments_menu') {
                    $comments_hook_found = true;
                    echo '<p style="color: green;">✅ show_comments_menu hook\'u admin_menu\'ye eklenmiş (Priority: ' . $priority . ')</p>';
                    break 2;
                }
            }
        }
    }
}

if (!$comments_hook_found) {
    echo '<p style="color: red;">❌ show_comments_menu hook\'u admin_menu\'ye eklenmemiş</p>';
}

// 8. Tutor LMS'in filter_posts_for_instructors fonksiyonunu kontrol et
if (class_exists('TUTOR\Admin')) {
    echo '<p style="color: green;">✅ Tutor Admin sınıfı mevcut</p>';

    // Tutor Admin instance'ını al
    $tutor_admin = new \TUTOR\Admin();
    if (method_exists($tutor_admin, 'filter_posts_for_instructors')) {
        echo '<p style="color: green;">✅ filter_posts_for_instructors fonksiyonu mevcut</p>';
    } else {
        echo '<p style="color: red;">❌ filter_posts_for_instructors fonksiyonu bulunamadı</p>';
    }
} else {
    echo '<p style="color: orange;">⚠️ Tutor Admin sınıfı bulunamadı</p>';
}

// 9. WordPress admin menü hook'larını listele
echo '<h3 style="color: #333; margin-top: 20px;">🔧 Admin Menu Hook Detayları</h3>';
$admin_menu_hooks = $GLOBALS['wp_filter']['admin_menu'] ?? null;

if ($admin_menu_hooks) {
    echo '<div style="background: #fff; padding: 15px; border-radius: 4px; margin: 10px 0;">';
    echo '<h4>Admin Menu Hook\'ları:</h4>';
    echo '<table style="width: 100%; border-collapse: collapse;">';
    echo '<tr style="background: #f9f9f9;"><th style="border: 1px solid #ddd; padding: 8px;">Priority</th><th style="border: 1px solid #ddd; padding: 8px;">Fonksiyon</th><th style="border: 1px solid #ddd; padding: 8px;">Sınıf</th></tr>';

    foreach ($admin_menu_hooks->callbacks as $priority => $callbacks) {
        foreach ($callbacks as $callback) {
            $function_name = 'Bilinmeyen';
            $class_name = 'N/A';

            if (isset($callback['function'])) {
                if (is_array($callback['function'])) {
                    $object = $callback['function'][0] ?? null;
                    $method = $callback['function'][1] ?? null;

                    if (is_object($object)) {
                        $class_name = get_class($object);
                        $function_name = $method;
                    } elseif (is_string($object)) {
                        $class_name = $object;
                        $function_name = $method;
                    }
                } elseif (is_string($callback['function'])) {
                    $function_name = $callback['function'];
                }
            }

            $row_color = '';
            if (strpos($class_name, 'Role_Custom') !== false) {
                $row_color = 'background: #e8f5e8;';
            } elseif (strpos($class_name, 'TUTOR') !== false) {
                $row_color = 'background: #fff3cd;';
            }

            echo '<tr style="' . $row_color . '">';
            echo '<td style="border: 1px solid #ddd; padding: 8px;">' . $priority . '</td>';
            echo '<td style="border: 1px solid #ddd; padding: 8px;">' . $function_name . '</td>';
            echo '<td style="border: 1px solid #ddd; padding: 8px;">' . $class_name . '</td>';
            echo '</tr>';
        }
    }
    echo '</table>';
    echo '</div>';
}

echo '<hr style="margin: 20px 0;">';
echo '<h3 style="color: #333;">📝 Öneriler ve Çözümler:</h3>';

if (!$comments_menu_found) {
    echo '<div style="background: #fff3cd; padding: 15px; border-radius: 4px; border-left: 4px solid #ffc107;">';
    echo '<h4 style="margin-top: 0; color: #856404;">⚠️ Yorumlar menüsü bulunamadı</h4>';
    echo '<ul style="margin-bottom: 0;">';
    echo '<li>WordPress admin panelini yenileyin (F5 veya Ctrl+R)</li>';
    echo '<li>Role Custom eklentisini devre dışı bırakıp tekrar etkinleştirin</li>';
    echo '<li>Tutor instructor rolünde bir kullanıcı ile giriş yapın</li>';
    echo '<li>Tarayıcı cache\'ini temizleyin</li>';
    echo '<li>Hook priority\'sinin doğru olduğundan emin olun (999999)</li>';
    echo '</ul>';
    echo '</div>';
} else {
    echo '<div style="background: #d4edda; padding: 15px; border-radius: 4px; border-left: 4px solid #28a745;">';
    echo '<h4 style="margin-top: 0; color: #155724;">✅ Başarılı!</h4>';
    echo '<p style="margin-bottom: 0;">Yorumlar menüsü başarıyla eklendi ve görünür durumda.</p>';
    echo '</div>';
}

// Debug bilgileri
echo '<div style="background: #f8f9fa; padding: 15px; border-radius: 4px; margin-top: 20px;">';
echo '<h4 style="margin-top: 0; color: #495057;">🔍 Debug Bilgileri</h4>';
echo '<p><strong>WordPress Sürümü:</strong> ' . get_bloginfo('version') . '</p>';
echo '<p><strong>PHP Sürümü:</strong> ' . PHP_VERSION . '</p>';
echo '<p><strong>Aktif Tema:</strong> ' . wp_get_theme()->get('Name') . '</p>';
echo '<p><strong>Mevcut Sayfa:</strong> ' . (isset($_GET['page']) ? $_GET['page'] : 'Bilinmiyor') . '</p>';
echo '<p><strong>Admin URL:</strong> ' . admin_url() . '</p>';
echo '</div>';

echo '</div>';
